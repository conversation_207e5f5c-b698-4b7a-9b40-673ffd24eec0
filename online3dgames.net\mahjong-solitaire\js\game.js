/**
 * Mahjong Connect Game Engine
 */

class MahjongConnectGame {
    constructor() {
        this.tileSet = new MahjongTileSet();
        this.selectedTiles = [];
        this.score = 0;
        this.startTime = null;
        this.gameTime = 0;
        this.gameTimer = null;
        this.isGameActive = false;
        this.hintsUsed = 0;
        this.tileWidth = 50;
        this.tileHeight = 70;
        this.tileSpacing = 5;
        this.connectionLines = [];
        
        this.initializeGame();
        this.setupEventListeners();
    }

    /**
     * Initialize the game
     */
    initializeGame() {
        this.gameBoard = document.getElementById('mahjongGrid');
        this.hideLoadingScreen();
        this.newGame();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Game buttons
        document.getElementById('restartBtn').addEventListener('click', () => this.newGame());
        document.getElementById('hintBtn').addEventListener('click', () => this.showHint());
        document.getElementById('rulesBtn').addEventListener('click', () => this.showRules());
        document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());

        // Modal buttons
        document.getElementById('closeRulesBtn').addEventListener('click', () => this.hideRules());
        document.getElementById('closeRulesOkBtn').addEventListener('click', () => this.hideRules());
        document.getElementById('newGameBtn').addEventListener('click', () => this.newGame());
        document.getElementById('closeMessageBtn').addEventListener('click', () => this.hideGameMessage());

        // Tile click events will be added dynamically
    }

    /**
     * Start a new game
     */
    newGame() {
        this.clearGame();
        this.generateTiles();
        this.renderTiles();
        this.startTimer();
        this.isGameActive = true;
        this.updateGameStats();
    }

    /**
     * Clear current game state
     */
    clearGame() {
        this.selectedTiles = [];
        this.score = 0;
        this.hintsUsed = 0;
        this.gameTime = 0;
        this.clearConnectionLines();
        
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }
        
        // Clear game board
        if (this.gameBoard) {
            this.gameBoard.innerHTML = '';
        }
    }

    /**
     * Generate tiles for the game
     */
    generateTiles() {
        this.tileSet.generateTiles();
    }

    /**
     * Render tiles on the game board
     */
    renderTiles() {
        if (!this.gameBoard) return;

        const boardRect = this.gameBoard.getBoundingClientRect();
        const boardWidth = boardRect.width;
        const boardHeight = boardRect.height;
        
        // Calculate grid dimensions
        const cols = this.tileSet.gridCols;
        const rows = this.tileSet.gridRows;
        
        // Calculate tile positions
        const totalTileWidth = cols * this.tileWidth + (cols - 1) * this.tileSpacing;
        const totalTileHeight = rows * this.tileHeight + (rows - 1) * this.tileSpacing;
        
        const startX = (boardWidth - totalTileWidth) / 2;
        const startY = (boardHeight - totalTileHeight) / 2;

        this.tileSet.tiles.forEach(tile => {
            if (!tile.removed) {
                const x = startX + tile.col * (this.tileWidth + this.tileSpacing);
                const y = startY + tile.row * (this.tileHeight + this.tileSpacing);
                
                tile.setPosition(x, y);
                const element = tile.createElement();
                
                // Add click event listener
                element.addEventListener('click', (e) => this.handleTileClick(tile, e));
                
                this.gameBoard.appendChild(element);
            }
        });
    }

    /**
     * Handle tile click
     */
    handleTileClick(tile, event) {
        if (!this.isGameActive || tile.removed) return;

        event.preventDefault();
        event.stopPropagation();

        if (tile.selected) {
            // Deselect tile
            this.deselectTile(tile);
        } else if (this.selectedTiles.length < 2) {
            // Select tile
            this.selectTile(tile);
            
            if (this.selectedTiles.length === 2) {
                this.checkConnection();
            }
        }
    }

    /**
     * Select a tile
     */
    selectTile(tile) {
        tile.setSelected(true);
        this.selectedTiles.push(tile);
    }

    /**
     * Deselect a tile
     */
    deselectTile(tile) {
        tile.setSelected(false);
        const index = this.selectedTiles.indexOf(tile);
        if (index > -1) {
            this.selectedTiles.splice(index, 1);
        }
    }

    /**
     * Check if selected tiles can be connected
     */
    checkConnection() {
        if (this.selectedTiles.length !== 2) return;

        const [tile1, tile2] = this.selectedTiles;
        
        if (tile1.canMatch(tile2)) {
            const connectionResult = this.tileSet.canConnect(tile1, tile2);
            
            if (connectionResult) {
                // Valid connection found
                this.showConnectionPath(connectionResult.path);
                setTimeout(() => {
                    this.removeTiles(tile1, tile2);
                    this.clearConnectionLines();
                }, 800);
            } else {
                // No valid path
                this.showInvalidConnection();
            }
        } else {
            // Tiles don't match
            this.showInvalidConnection();
        }
    }

    /**
     * Show connection path
     */
    showConnectionPath(path) {
        this.clearConnectionLines();
        
        for (let i = 0; i < path.length - 1; i++) {
            const start = path[i];
            const end = path[i + 1];
            this.drawConnectionLine(start, end);
        }
    }

    /**
     * Draw connection line between two points
     */
    drawConnectionLine(start, end) {
        const line = document.createElement('div');
        line.className = 'connection-line';
        
        const boardRect = this.gameBoard.getBoundingClientRect();
        const cols = this.tileSet.gridCols;
        const rows = this.tileSet.gridRows;
        
        const totalTileWidth = cols * this.tileWidth + (cols - 1) * this.tileSpacing;
        const totalTileHeight = rows * this.tileHeight + (rows - 1) * this.tileSpacing;
        
        const startX = (boardRect.width - totalTileWidth) / 2;
        const startY = (boardRect.height - totalTileHeight) / 2;
        
        const x1 = startX + start.col * (this.tileWidth + this.tileSpacing) + this.tileWidth / 2;
        const y1 = startY + start.row * (this.tileHeight + this.tileSpacing) + this.tileHeight / 2;
        const x2 = startX + end.col * (this.tileWidth + this.tileSpacing) + this.tileWidth / 2;
        const y2 = startY + end.row * (this.tileHeight + this.tileSpacing) + this.tileHeight / 2;
        
        if (start.row === end.row) {
            // Horizontal line
            line.classList.add('horizontal');
            line.style.left = `${Math.min(x1, x2)}px`;
            line.style.top = `${y1 - 1.5}px`;
            line.style.width = `${Math.abs(x2 - x1)}px`;
        } else {
            // Vertical line
            line.classList.add('vertical');
            line.style.left = `${x1 - 1.5}px`;
            line.style.top = `${Math.min(y1, y2)}px`;
            line.style.height = `${Math.abs(y2 - y1)}px`;
        }
        
        this.gameBoard.appendChild(line);
        this.connectionLines.push(line);
    }

    /**
     * Clear all connection lines
     */
    clearConnectionLines() {
        this.connectionLines.forEach(line => {
            if (line.parentNode) {
                line.parentNode.removeChild(line);
            }
        });
        this.connectionLines = [];
    }

    /**
     * Show invalid connection feedback
     */
    showInvalidConnection() {
        this.selectedTiles.forEach(tile => {
            tile.element.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                if (tile.element) {
                    tile.element.style.animation = '';
                }
            }, 500);
        });
        
        setTimeout(() => {
            this.selectedTiles.forEach(tile => this.deselectTile(tile));
        }, 600);
    }

    /**
     * Remove matched tiles
     */
    removeTiles(tile1, tile2) {
        this.tileSet.removeTile(tile1);
        this.tileSet.removeTile(tile2);
        
        this.selectedTiles = [];
        this.score += 10;
        this.updateGameStats();
        
        // Check win condition
        if (this.tileSet.isComplete()) {
            this.gameWon();
        } else if (!this.tileSet.hasValidMoves()) {
            this.gameOver();
        }
    }

    /**
     * Start game timer
     */
    startTimer() {
        this.startTime = Date.now();
        this.gameTimer = setInterval(() => {
            this.gameTime = Math.floor((Date.now() - this.startTime) / 1000);
            this.updateTimeDisplay();
        }, 1000);
    }

    /**
     * Update time display
     */
    updateTimeDisplay() {
        const minutes = Math.floor(this.gameTime / 60);
        const seconds = this.gameTime % 60;
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('timeDisplay').textContent = `Time: ${timeString}`;
    }

    /**
     * Update game statistics display
     */
    updateGameStats() {
        document.getElementById('scoreDisplay').textContent = `Score: ${this.score}`;
        document.getElementById('tilesLeft').textContent = `Tiles: ${this.tileSet.getRemainingTilesCount()}`;
    }

    /**
     * Show hint
     */
    showHint() {
        if (!this.isGameActive) return;
        
        const hint = this.tileSet.getHint();
        if (hint) {
            this.hintsUsed++;
            this.score = Math.max(0, this.score - 5);
            this.updateGameStats();
            
            hint.forEach(tile => {
                tile.element.classList.add('hint-highlight');
                setTimeout(() => {
                    if (tile.element) {
                        tile.element.classList.remove('hint-highlight');
                    }
                }, 2000);
            });
        }
    }

    /**
     * Game won
     */
    gameWon() {
        this.isGameActive = false;
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
        }
        
        const timeBonus = Math.max(0, 300 - this.gameTime) * 2;
        this.score += timeBonus;
        this.updateGameStats();
        
        this.showGameMessage('Congratulations!', 
            `You completed the game in ${this.formatTime(this.gameTime)}!<br>
             Final Score: ${this.score}<br>
             Hints Used: ${this.hintsUsed}`);
    }

    /**
     * Game over
     */
    gameOver() {
        this.isGameActive = false;
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
        }
        
        this.showGameMessage('Game Over', 'No more moves available. Try again!');
    }

    /**
     * Format time for display
     */
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    /**
     * Show game message modal
     */
    showGameMessage(title, message) {
        document.getElementById('messageTitle').textContent = title;
        document.getElementById('messageText').innerHTML = message;
        document.getElementById('gameMessage').style.display = 'flex';
    }

    /**
     * Hide game message modal
     */
    hideGameMessage() {
        document.getElementById('gameMessage').style.display = 'none';
    }

    /**
     * Show rules modal
     */
    showRules() {
        document.getElementById('rulesModal').style.display = 'flex';
    }

    /**
     * Hide rules modal
     */
    hideRules() {
        document.getElementById('rulesModal').style.display = 'none';
    }

    /**
     * Toggle fullscreen
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }
}

// Initialize game when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.mahjongGame = new MahjongConnectGame();
});
