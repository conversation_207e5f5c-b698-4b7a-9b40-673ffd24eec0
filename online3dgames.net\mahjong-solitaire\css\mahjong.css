/* Mahjong Connect Tiles Styles */

.mahjong-tile {
    width: 50px;
    height: 70px;
    background: #fefefe;
    border: 2px solid #ccc;
    border-radius: 8px;
    position: absolute;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 2px 2px 6px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 42px;
    line-height: 1;
    color: #333;
    overflow: hidden;
    user-select: none;
    z-index: 1;
}



.mahjong-tile:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 3px 3px 8px rgba(0,0,0,0.4);
    filter: brightness(1.1);
}

.mahjong-tile.selected {
    background: #fff8dc;
    border-color: #daa520;
    transform: translateY(-3px) scale(1.08);
    box-shadow:
        0 6px 12px rgba(218, 165, 32, 0.6),
        0 0 0 3px #daa520;
    color: #8b4513;
    z-index: 10;
}

.mahjong-tile.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    filter: grayscale(0.7);
}

.mahjong-tile.connectable {
    border-color: #4CAF50;
    box-shadow: 0 0 10px rgba(76, 175, 80, 0.6);
}

.mahjong-tile.hint {
    animation: hintPulse 1.5s ease-in-out infinite;
    z-index: 5;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 
            2px 2px 4px rgba(0,0,0,0.3),
            inset 0 0 0 1px rgba(255,255,255,0.8);
    }
    50% {
        box-shadow: 
            0 0 20px rgba(255, 215, 0, 0.8),
            inset 0 0 0 2px #ffd700;
        transform: translateY(-1px) scale(1.02);
    }
}

.mahjong-tile.removing {
    animation: tileRemove 0.5s ease-in-out forwards;
    z-index: 15;
}

@keyframes tileRemove {
    0% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(0) rotate(15deg);
        opacity: 0;
    }
}

.mahjong-tile.appearing {
    animation: tileAppear 0.5s ease-out forwards;
}

@keyframes tileAppear {
    0% {
        transform: scale(0) rotate(-15deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.1) rotate(-5deg);
        opacity: 0.8;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* Different types of mahjong tiles */
.tile-wan { 
    color: #d32f2f; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-tong { 
    color: #1976d2; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-tiao { 
    color: #388e3c; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-feng { 
    color: #7b1fa2; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-jian { 
    color: #d32f2f; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-hua { 
    color: #ff6f00; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.tile-season { 
    color: #c2185b; 
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

/* Special color handling */
.tile-jian[data-value="fa"] {
    filter: hue-rotate(120deg) brightness(1.1) contrast(1.2);
}

.tile-special {
    background: linear-gradient(145deg, #fff8dc 0%, #f0e68c 50%, #daa520 100%);
    border-color: #b8860b;
}

.tile-back {
    background: linear-gradient(145deg, #2d5a2d 0%, #1a4d1a 100%);
    border-color: #1a4d1a;
    color: #ffffff;
    font-size: 32px;
    filter: none;
}

.tile-back::before {
    background: linear-gradient(135deg, #1a4d1a 0%, #0d2d0d 100%) !important;
}

/* Connection line styles */
.connection-line {
    position: absolute;
    background: #4CAF50;
    z-index: 100;
    pointer-events: none;
    opacity: 0.8;
    border-radius: 2px;
    animation: connectionAppear 0.3s ease-out;
}

.connection-line.horizontal {
    height: 3px;
}

.connection-line.vertical {
    width: 3px;
}

@keyframes connectionAppear {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    100% {
        opacity: 0.8;
        transform: scale(1);
    }
}

/* Match effect */
.mahjong-tile.matching {
    animation: matchEffect 0.5s ease-in-out;
}

@keyframes matchEffect {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        box-shadow:
            0 0 20px rgba(76, 175, 80, 0.8),
            0 0 0 3px #4caf50;
    }
    100% {
        transform: scale(0);
        opacity: 0;
    }
}

/* Hint effect */
.mahjong-tile.hint-highlight {
    animation: hintPulse 1.5s ease-in-out infinite;
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 2px 2px 6px rgba(0,0,0,0.3);
    }
    50% {
        box-shadow:
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 0 3px #ffd700;
        transform: translateY(-2px) scale(1.05);
    }
}

/* Shake animation for invalid connections */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Responsive tile sizing */
@media (max-width: 768px) {
    .mahjong-tile {
        width: 40px;
        height: 56px;
        font-size: 32px;
    }
    
    .tile-back {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .mahjong-tile {
        width: 35px;
        height: 49px;
        font-size: 28px;
    }
    
    .tile-back {
        font-size: 20px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mahjong-tile {
        box-shadow: 
            1px 1px 2px rgba(0,0,0,0.3),
            inset 0 0 0 0.5px rgba(255,255,255,0.8);
    }
    
    .mahjong-tile::after {
        border-width: 0.5px;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .mahjong-tile,
    .mahjong-tile:hover,
    .mahjong-tile.selected {
        transition: none;
        animation: none;
    }
    
    .mahjong-tile.hint {
        animation: none;
        box-shadow: 
            0 0 10px rgba(255, 215, 0, 0.8),
            inset 0 0 0 2px #ffd700;
    }
}

/* Focus styles for keyboard navigation */
.mahjong-tile:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* Print styles */
@media print {
    .mahjong-tile {
        box-shadow: none;
        border: 2px solid #000;
        background: #fff;
        color: #000;
    }
}
