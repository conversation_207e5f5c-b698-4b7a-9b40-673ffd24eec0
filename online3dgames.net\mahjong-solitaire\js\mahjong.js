/**
 * Mahjong Connect - Tile System and Data Structures
 */

class MahjongTile {
    constructor(type, value, unicode, id, row, col) {
        this.type = type;           // 'wan', 'tong', 'tiao', 'feng', 'jian', 'hua', 'season'
        this.value = value;         // 1-9 for numbered tiles, specific values for others
        this.unicode = unicode;     // Unicode character for display
        this.id = id;              // Unique identifier
        this.row = row;            // Grid row position
        this.col = col;            // Grid column position
        this.x = 0;                // Pixel X position
        this.y = 0;                // Pixel Y position
        this.element = null;       // DOM element
        this.selected = false;     // Currently selected
        this.removed = false;      // Has been removed
    }

    /**
     * Check if this tile can match with another tile
     */
    canMatch(otherTile) {
        if (!otherTile || this.removed || otherTile.removed) {
            return false;
        }

        // Exact match for most tiles
        if (this.type === otherTile.type && this.value === otherTile.value) {
            return true;
        }

        // Special matching rules for flower and season tiles
        if (this.type === 'hua' && otherTile.type === 'hua') {
            return true; // Any flower matches any flower
        }

        if (this.type === 'season' && otherTile.type === 'season') {
            return true; // Any season matches any season
        }

        return false;
    }

    /**
     * Get CSS classes for this tile
     */
    getCSSClasses() {
        const classes = ['mahjong-tile', `tile-${this.type}`];

        if (this.selected) classes.push('selected');
        if (this.removed) classes.push('removed');

        return classes.join(' ');
    }

    /**
     * Create DOM element for this tile
     */
    createElement() {
        if (this.element) {
            return this.element;
        }

        const element = document.createElement('div');
        element.className = this.getCSSClasses();
        element.textContent = this.unicode;
        element.dataset.tileId = this.id;
        element.dataset.type = this.type;
        element.dataset.value = this.value;
        element.dataset.row = this.row;
        element.dataset.col = this.col;

        // Position the element
        element.style.left = `${this.x}px`;
        element.style.top = `${this.y}px`;

        this.element = element;
        return element;
    }

    /**
     * Update tile position
     */
    setPosition(x, y) {
        this.x = x;
        this.y = y;

        if (this.element) {
            this.element.style.left = `${x}px`;
            this.element.style.top = `${y}px`;
        }
    }

    /**
     * Select/deselect tile
     */
    setSelected(selected) {
        this.selected = selected;
        if (this.element) {
            this.element.className = this.getCSSClasses();
        }
    }

    /**
     * Remove tile from game
     */
    remove() {
        this.removed = true;
        this.selected = false;

        if (this.element) {
            this.element.classList.add('matching');
            setTimeout(() => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
            }, 500);
        }
    }
}

class MahjongTileSet {
    constructor() {
        this.tiles = [];
        this.tileCounter = 0;
        this.grid = [];
        this.gridRows = 12;
        this.gridCols = 16;
    }

    /**
     * Generate tiles for pyramid layout
     */
    generateTiles() {
        this.tiles = [];
        this.tileCounter = 0;
        this.initializeGrid();

        // Generate pairs of tiles for connect game
        this.generateTilePairs();

        // Shuffle and place tiles in pyramid layout
        this.shuffleTiles();
        this.placeTilesInPyramid();

        return this.tiles;
    }

    /**
     * Initialize empty grid
     */
    initializeGrid() {
        this.grid = [];
        for (let row = 0; row < this.gridRows; row++) {
            this.grid[row] = [];
            for (let col = 0; col < this.gridCols; col++) {
                this.grid[row][col] = null;
            }
        }
    }

    /**
     * Generate pairs of tiles for connect game
     */
    generateTilePairs() {
        const tileTypes = [
            // Wan (Characters) - 9 types
            { type: 'wan', unicodes: ['🀇', '🀈', '🀉', '🀊', '🀋', '🀌', '🀍', '🀎', '🀏'] },
            // Tong (Circles) - 9 types
            { type: 'tong', unicodes: ['🀙', '🀚', '🀛', '🀜', '🀝', '🀞', '🀟', '🀠', '🀡'] },
            // Tiao (Bamboos) - 9 types
            { type: 'tiao', unicodes: ['🀐', '🀑', '🀒', '🀓', '🀔', '🀕', '🀖', '🀗', '🀘'] },
            // Wind tiles - 4 types
            { type: 'feng', unicodes: ['🀀', '🀁', '🀂', '🀃'] },
            // Dragon tiles - 3 types
            { type: 'jian', unicodes: ['🀄', '🀅', '🀆'] },
            // Flower tiles - 4 types
            { type: 'hua', unicodes: ['🀢', '🀣', '🀤', '🀥'] },
            // Season tiles - 4 types
            { type: 'season', unicodes: ['🀦', '🀧', '🀨', '🀩'] }
        ];

        // Generate pairs for each tile type
        tileTypes.forEach(typeData => {
            typeData.unicodes.forEach((unicode, index) => {
                // Create a pair of each tile
                for (let pair = 0; pair < 2; pair++) {
                    const tile = new MahjongTile(
                        typeData.type,
                        index + 1,
                        unicode,
                        this.tileCounter++,
                        0, 0 // row, col will be set later
                    );
                    this.tiles.push(tile);
                }
            });
        });
    }

    /**
     * Shuffle tiles array
     */
    shuffleTiles() {
        for (let i = this.tiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.tiles[i], this.tiles[j]] = [this.tiles[j], this.tiles[i]];
        }
    }

    /**
     * Place tiles in pyramid layout
     */
    placeTilesInPyramid() {
        const pyramidPositions = this.generatePyramidPositions();

        // Ensure we don't exceed available tiles
        const maxTiles = Math.min(this.tiles.length, pyramidPositions.length);

        for (let i = 0; i < maxTiles; i++) {
            const tile = this.tiles[i];
            const pos = pyramidPositions[i];

            tile.row = pos.row;
            tile.col = pos.col;
            this.grid[pos.row][pos.col] = tile;
        }

        // Remove excess tiles if any
        this.tiles = this.tiles.slice(0, maxTiles);
    }

    /**
     * Generate pyramid layout positions
     */
    generatePyramidPositions() {
        const positions = [];
        const centerRow = Math.floor(this.gridRows / 2);
        const centerCol = Math.floor(this.gridCols / 2);

        // Create pyramid shape - wider at bottom, narrower at top
        // Adjust to create exactly the right number of positions for our tiles
        const levels = [
            { width: 14, offset: 0 },
            { width: 12, offset: 1 },
            { width: 10, offset: 2 },
            { width: 8, offset: 3 },
            { width: 6, offset: 4 },
            { width: 4, offset: 5 },
            { width: 2, offset: 6 }
        ];

        levels.forEach((level, levelIndex) => {
            const row = centerRow - 3 + levelIndex;
            const startCol = centerCol - Math.floor(level.width / 2);

            if (row >= 0 && row < this.gridRows) {
                for (let i = 0; i < level.width; i++) {
                    const col = startCol + i;
                    if (col >= 0 && col < this.gridCols) {
                        positions.push({ row, col });
                    }
                }
            }
        });

        return positions;
    }
    /**
     * Check if two tiles can be connected with at most 3 lines
     */
    canConnect(tile1, tile2) {
        if (!tile1 || !tile2 || tile1.removed || tile2.removed || tile1.id === tile2.id) {
            return false;
        }

        // Try direct connection (0 turns)
        if (this.hasDirectPath(tile1.row, tile1.col, tile2.row, tile2.col)) {
            return { path: this.getDirectPath(tile1.row, tile1.col, tile2.row, tile2.col), turns: 0 };
        }

        // Try 1-turn connection
        const oneTurnPath = this.findOneTurnPath(tile1.row, tile1.col, tile2.row, tile2.col);
        if (oneTurnPath) {
            return { path: oneTurnPath, turns: 1 };
        }

        // Try 2-turn connection
        const twoTurnPath = this.findTwoTurnPath(tile1.row, tile1.col, tile2.row, tile2.col);
        if (twoTurnPath) {
            return { path: twoTurnPath, turns: 2 };
        }

        return false;
    }

    /**
     * Check if there's a direct path between two points
     */
    hasDirectPath(row1, col1, row2, col2) {
        // Same row - horizontal line
        if (row1 === row2) {
            const startCol = Math.min(col1, col2) + 1;
            const endCol = Math.max(col1, col2);
            for (let col = startCol; col < endCol; col++) {
                if (this.grid[row1][col] && !this.grid[row1][col].removed) {
                    return false;
                }
            }
            return true;
        }

        // Same column - vertical line
        if (col1 === col2) {
            const startRow = Math.min(row1, row2) + 1;
            const endRow = Math.max(row1, row2);
            for (let row = startRow; row < endRow; row++) {
                if (this.grid[row][col1] && !this.grid[row][col1].removed) {
                    return false;
                }
            }
            return true;
        }

        return false;
    }

    /**
     * Get direct path between two points
     */
    getDirectPath(row1, col1, row2, col2) {
        return [
            { row: row1, col: col1 },
            { row: row2, col: col2 }
        ];
    }

    /**
     * Find one-turn path (L-shaped)
     */
    findOneTurnPath(row1, col1, row2, col2) {
        // Try corner at (row1, col2)
        if (this.isValidCorner(row1, col2) &&
            this.hasDirectPath(row1, col1, row1, col2) &&
            this.hasDirectPath(row1, col2, row2, col2)) {
            return [
                { row: row1, col: col1 },
                { row: row1, col: col2 },
                { row: row2, col: col2 }
            ];
        }

        // Try corner at (row2, col1)
        if (this.isValidCorner(row2, col1) &&
            this.hasDirectPath(row1, col1, row2, col1) &&
            this.hasDirectPath(row2, col1, row2, col2)) {
            return [
                { row: row1, col: col1 },
                { row: row2, col: col1 },
                { row: row2, col: col2 }
            ];
        }

        return null;
    }

    /**
     * Find two-turn path (Z-shaped)
     */
    findTwoTurnPath(row1, col1, row2, col2) {
        // Try all possible intermediate points
        for (let row = 0; row < this.gridRows; row++) {
            for (let col = 0; col < this.gridCols; col++) {
                if (this.isValidCorner(row, col)) {
                    // Check if we can connect through this point
                    const path1 = this.findOneTurnPath(row1, col1, row, col);
                    const path2 = this.findOneTurnPath(row, col, row2, col2);

                    if (path1 && path2) {
                        // Combine paths, removing duplicate middle point
                        const combinedPath = [...path1];
                        combinedPath.push(...path2.slice(1));
                        return combinedPath;
                    }
                }
            }
        }
        return null;
    }

    /**
     * Check if a position is valid for corner (empty or edge)
     */
    isValidCorner(row, col) {
        // Edge positions (outside grid) are valid
        if (row < 0 || row >= this.gridRows || col < 0 || col >= this.gridCols) {
            return true;
        }
        // Grid positions must be empty
        return !this.grid[row][col] || this.grid[row][col].removed;
    }

    /**
     * Get tile at specific grid position
     */
    getTileAt(row, col) {
        if (row < 0 || row >= this.gridRows || col < 0 || col >= this.gridCols) {
            return null;
        }
        return this.grid[row][col];
    }

    /**
     * Remove tile from grid
     */
    removeTile(tile) {
        if (tile && !tile.removed) {
            tile.remove();
            this.grid[tile.row][tile.col] = null;
        }
    }

    /**
     * Check if there are any valid moves available
     */
    hasValidMoves() {
        const activeTiles = this.tiles.filter(tile => !tile.removed);

        for (let i = 0; i < activeTiles.length; i++) {
            for (let j = i + 1; j < activeTiles.length; j++) {
                const tile1 = activeTiles[i];
                const tile2 = activeTiles[j];

                if (tile1.canMatch(tile2) && this.canConnect(tile1, tile2)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Shuffle tiles array
     */
    shuffle() {
        for (let i = this.tiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.tiles[i], this.tiles[j]] = [this.tiles[j], this.tiles[i]];
        }
        return this.tiles;
    }

    /**
     * Get tile by ID
     */
    getTileById(id) {
        return this.tiles.find(tile => tile.id === parseInt(id));
    }

    /**
     * Get all tiles of a specific type
     */
    getTilesByType(type) {
        return this.tiles.filter(tile => tile.type === type && !tile.removed);
    }

    /**
     * Get hint (first available matching pair)
     */
    getHint() {
        const activeTiles = this.tiles.filter(tile => !tile.removed);

        for (let i = 0; i < activeTiles.length; i++) {
            for (let j = i + 1; j < activeTiles.length; j++) {
                const tile1 = activeTiles[i];
                const tile2 = activeTiles[j];

                if (tile1.canMatch(tile2) && this.canConnect(tile1, tile2)) {
                    return [tile1, tile2];
                }
            }
        }

        return null;
    }

    /**
     * Get remaining tiles count
     */
    getRemainingTilesCount() {
        return this.tiles.filter(tile => !tile.removed).length;
    }

    /**
     * Check if game is complete
     */
    isComplete() {
        return this.tiles.every(tile => tile.removed);
    }

}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MahjongTile, MahjongTileSet };

